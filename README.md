# LLM 工具调用
---

> 注意：
> 本示例中使用的是百炼大模型，可替换为其他 openai 兼容的大模型。将.env.example 重命名为 .env 并填写相关信息。

## 原理

大模型在分析用户问题时，会根据问题和工具的描述，决定是否使用工具。使用工具时，会返回需要使用的工具（即预设好的函数），并提供工具需要的参数。客户端根据这个信息，调用工具，并将这个结果返回给大模型，大模型根据这个结果，给出最终的回答。

实现上述效果的前提：
1. 大模型需要支持工具调用（能够获取到工具，并给出执行工具需要的参数）
2. 客户端需要能够按照大模型给出的参数，调用工具

## 实现流程
```mermaid

graph TD
    A[用户输入问题] --> B[获取大模型返回的内容]
    B --> C{是否需要使用工具}
    C -->|是| D[根据大模型返回的工具调用信息，调用工具]
    C -->|否| E[直接返回大模型返回的内容]
    D --> F[将结果返回给大模型]
    E --> F
    F --> G[大模型根据这个结果，给出最终的回答]
```

## 使用方法

修改完.env 文件后，运行 `npm install` 安装依赖。

运行 `npm run start` 启动项目，开始提问（目前只有天气和时间两个工具）。


## 返回示例
在这个项目中，大模型返回的示例如下：

- 需要使用工具时返回：

用户输入：
```bash
现在几点了
```
大模型返回：
```json
{
  "content": "",
  "role": "assistant",
  "tool_calls": [
    {
      "function": [
        { "name": "get_current_time", "arguments": "{}" } 
      ],
      "index": 0,
      "id": "call_a328138c6d6643a8a55f1d",
      "type": "function"
    }
  ]
} 
 
```

- 不需要使用工具时返回：

用户输入：
```bash
你好
```
大模型返回：
```json
{ 
  "content": "你好！有什么我可以帮你的吗？",
  "role": "assistant"
} 
```
