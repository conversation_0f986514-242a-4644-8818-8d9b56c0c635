{"parameters": {"type": "object", "properties": {"type": {"type": "string", "description": "图表类型，如line、bar、pie、doughnut、radar、scatter、polarArea、bubble等。"}, "data": {"type": "object", "description": "图表数据，包含labels和datasets等信息。", "properties": {"labels": {"type": "array", "description": "坐标轴标签数组，用于标识数据类别。"}, "datasets": {"type": "array", "description": "数据集数组，每个数据集包含具体数据及样式配置。", "items": {"type": "object", "properties": {"data": {"type": "array", "description": "数据值数组，可直接为数值或包含x、y等属性的对象。"}, "label": {"type": "string", "description": "数据集标签，用于图例显示。"}, "backgroundColor": {"type": "string", "description": "数据元素填充颜色。"}, "borderColor": {"type": "string", "description": "数据元素边框颜色。"}, "borderWidth": {"type": "number", "description": "数据元素边框宽度。"}}}}}}, "options": {"type": "object", "description": "图表配置选项，包含布局、缩放、动画等设置。", "properties": {"responsive": {"type": "boolean", "description": "是否响应式布局，默认为true。"}, "scales": {"type": "object", "description": "坐标轴配置，包含x轴、y轴等设置。", "properties": {"x": {"type": "object", "description": "x轴配置。", "properties": {"type": {"type": "string", "description": "x轴类型，如linear、time、category等。"}, "display": {"type": "boolean", "description": "是否显示x轴。"}, "min": {"type": "number", "description": "x轴最小值。"}, "max": {"type": "number", "description": "x轴最大值。"}, "ticks": {"type": "object", "description": "x轴刻度配置。", "properties": {"autoSkip": {"type": "boolean", "description": "是否自动跳过刻度以避免重叠。"}, "stepSize": {"type": "number", "description": "刻度间隔步长。"}, "minRotation": {"type": "number", "description": "刻度标签最小旋转角度。"}, "maxRotation": {"type": "number", "description": "刻度标签最大旋转角度。"}}}}}, "y": {"type": "object", "description": "y轴配置，结构同x轴。"}}}, "layout": {"type": "object", "description": "图表布局配置。", "properties": {"padding": {"type": "object", "description": "图表内边距。", "properties": {"left": {"type": "number", "description": "左内边距。"}, "right": {"type": "number", "description": "右内边距。"}, "top": {"type": "number", "description": "上内边距。"}, "bottom": {"type": "number", "description": "下内边距。"}}}}}, "animation": {"type": "object", "description": "动画配置。", "properties": {"duration": {"type": "number", "description": "动画持续时间（毫秒）。"}, "easing": {"type": "string", "description": "动画缓动函数。"}}}, "plugins": {"type": "object", "description": "插件配置，如图例、标题等。", "properties": {"legend": {"type": "object", "description": "图例配置。", "properties": {"display": {"type": "boolean", "description": "是否显示图例。"}}}}}, "parsing": {"type": "object", "description": "数据解析配置，用于自定义数据属性映射。", "properties": {"xAxisKey": {"type": "string", "description": "映射到x轴的数据属性键名。"}, "yAxisKey": {"type": "string", "description": "映射到y轴的数据属性键名。"}, "key": {"type": "string", "description": "用于饼图等类型的数值属性键名。"}}}}}, "locale": {"type": "string", "description": "国际化配置，使用BCP 47语言标签，基于Intl.NumberFormat。"}}, "required": ["type", "data"]}}