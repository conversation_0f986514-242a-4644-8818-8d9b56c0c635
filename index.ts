import dotenv from "dotenv";
import OpenAI from "openai";
import { ChatCompletionMessageParam, ChatCompletionTool } from "openai/resources/index";
import readline from "readline"
import { tool_chart, generate_chart } from "./src/chart"
import app from "./src/server"

dotenv.config();
const { PORT ,API_KEY ,MODEL_NAME ,BASE_URL } = process.env;

console.log("环境变量已加载：",API_KEY, MODEL_NAME, BASE_URL, PORT);  

const openai = new OpenAI(
  {
      // 若没有配置环境变量，请用百炼API Key将下行替换为：apiKey: "sk-xxx",
      apiKey: API_KEY,
      baseURL: BASE_URL
  }
);
const systemPrompt = `
你是一个AI助手，请根据用户的问题，给出相应的回答。本次的静态资源服务地址为： http://localhost:${PORT}，当有静态资源是需要给用户提示预览地址。
`;

const tools: ChatCompletionTool[] = [
// 工具1 获取当前时刻的时间
{
  "type": "function",
  "function": {
      "name": "get_current_time",
      "description": "当你想知道现在的时间时非常有用。",
      // 因为获取当前时间无需输入参数，因此parameters为空
      "parameters": {}  
  }
},  
// 工具2 获取指定城市的天气
{
  "type": "function",
  "function": {
      "name": "get_current_weather",
      "description": "当你想查询指定城市的天气时非常有用。",
      "parameters": {  
          "type": "object",
          "properties": {
              // 查询天气时需要提供位置，因此参数设置为location
              "location": {
                  "type": "string",
                  "description": "城市或县区，比如北京市、杭州市、余杭区等。"
              }
          },
          "required": ["location"]
      }
    }
  }
];

function getCurrentTime(): string {
  console.log("调用工具get_current_time")
  return `当前时间是${new Date().toLocaleString()}`;
}
function getCurrentWeather(location: string): string {
  console.log("调用工具get_current_weather")
  return `当前${location}的天气是晴天`;
}
const toolFunctions:any = {
  get_current_time: getCurrentTime,
  get_current_weather: getCurrentWeather
}

tools.push(tool_chart)  
toolFunctions.generate_chart = generate_chart

// type Message = {
//   role: "user" | "assistant" | "tool";
//   content: string;
// }

type Message = ChatCompletionMessageParam

async function getLLMResponse(messages: Message[]) {
  const response = await openai.chat.completions.create({
      model: MODEL_NAME ?? "qwen-plus",  //此处以qwen-plus为例，可按需更换模型名称。模型列表：https://help.aliyun.com/zh/model-studio/getting-started/models
      messages,
      tools,
      tool_choice: "auto",
  });
  return response
}

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// rl.question("user: ", async (question) => {
//   const messages: Message[] = [
//     { role: "system", content: systemPrompt },
//     { role: "user", content: question }
//   ];
//   let i = 1;
//   const firstResponse = await getLLMResponse(messages);
//   let assistantOutput = firstResponse.choices[0].message;    
//   console.log(`第${i}轮大模型输出信息：${JSON.stringify(assistantOutput)}`);
//   if (Object.is(assistantOutput.content,null)){
//       assistantOutput.content = "";
//   }
//   messages.push(assistantOutput);
//   if (! ("tool_calls" in assistantOutput)) {
//       console.log(`无需调用工具，我可以直接回复：${assistantOutput.content}`);
//       rl.close();
//   } else{
//       while ("tool_calls" in assistantOutput) {
//           let toolInfo: Message = {role: "tool", content: "", tool_call_id: ""};
//           if (assistantOutput.tool_calls?.[0].function.name == "getCurrentWeather" ) {
//               toolInfo = {role: "tool", content: "", tool_call_id: ""};
//               let location = JSON.parse(assistantOutput.tool_calls?.[0].function.arguments)["location"];
//               toolInfo.content = getCurrentWeather(location);
//           } else if (assistantOutput.tool_calls?.[0].function.name == "getCurrentTime" ) {
//               toolInfo = {role: "tool", content: "", tool_call_id: ""};
//               toolInfo["content"] = getCurrentTime();
//           }
//           console.log(`工具输出信息：${JSON.stringify(toolInfo)}`);
//           console.log("=".repeat(100),"\n");
//           messages.push(toolInfo);
//           assistantOutput = (await getLLMResponse(messages)).choices[0].message;
//           if (Object.is(assistantOutput.content,null)){
//               assistantOutput.content = "";
//           }
//           messages.push(assistantOutput);
//           i += 1;
//           console.log(`第${i}轮大模型输出信息：${JSON.stringify(assistantOutput)}`)
//   }
//   console.log("=".repeat(100));
//   console.log(`最终大模型输出信息：${JSON.stringify(assistantOutput.content)}`);
//   rl.close();
//   }});


app.listen(PORT, () => {
  console.log(`开启静态资源服务，服务器在端口${PORT}上运行\nhttp://localhost:${PORT} \n`);
  rl.question("请输入问题：", async (question) => {
    const messages: Message[] = [
      { role: "system", content: systemPrompt },
      { role: "user", content: question }
    ];
    const response = await getLLMResponse(messages);
    // console.log(response.choices[0].message,"\n",response.choices[0].message.tool_calls?.[0].function,"\n",response);
    messages.push(response.choices[0].message) // 将大模型的返回的内容加入到messages中
    if(response.choices[0].message.tool_calls){
      // 需要使用工具
      const toolCalls = response.choices[0].message.tool_calls
      const toolCallFunctions = toolCalls.map(toolCall => toolCall.function)
      const toolCallResults: ChatCompletionMessageParam[] = toolCallFunctions.map(item=>{
        if(item.name in toolFunctions){
          return {content: toolFunctions[item.name as keyof typeof toolFunctions](item.arguments),role: "tool",tool_call_id: ""}
        }
        return {content: "此工具不存在！",role: "tool",tool_call_id: ""}
      })
      const finalResponse = await getLLMResponse([...messages, ...toolCallResults])
      console.log(finalResponse.choices[0].message.content)
    }else{
      // 不需要使用工具
      console.log(response.choices[0].message.content)
    }
    rl.close();
    // process.exit(0);
  });
}); 