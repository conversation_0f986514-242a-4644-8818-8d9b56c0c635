<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>图表生成结果</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    .chart-container {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      max-width: 900px;
      margin: 0 auto;
      backdrop-filter: blur(10px);
    }
    .chart-title {
      text-align: center;
      color: #333;
      margin-bottom: 20px;
      font-size: 24px;
      font-weight: 600;
    }
    canvas {
      max-width: 100%;
      height: auto;
    }
    .chart-info {
      margin-top: 20px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
      font-size: 14px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="chart-container">
    <h2 class="chart-title">图表生成结果</h2>
    <canvas id="myChart"></canvas>
    <div class="chart-info">
      <strong>图表类型:</strong> {"type": "bar", "data": {"labels": ["手机", "笔记本电脑", "平板", "耳机", "智能手表"], "datasets": [{"label": "销量（万台）", "data": [120, 85, 60, 150, 40], "backgroundColor": "rgba(54, 162, 235, 0.6)", "borderColor": "rgba(54, 162, 235, 1)", "borderWidth": 1}]}, "options": {"responsive": true, "scales": {"y": {"beginAtZero": true, "title": {"display": true, "text": "销量（万台）"}}, "x": {"title": {"display": true, "text": "产品类型"}}}, "plugins": {"legend": {"display": false}}}}<br>
      <strong>生成时间:</strong> 2025/7/30 19:44:52
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    // 错误处理
    try {
      const ctx = document.getElementById('myChart');
      if (!ctx) {
        throw new Error('Canvas element not found');
      }

      // Chart.js配置
      const chartConfig = {
  "type": "{\"type\": \"bar\", \"data\": {\"labels\": [\"手机\", \"笔记本电脑\", \"平板\", \"耳机\", \"智能手表\"], \"datasets\": [{\"label\": \"销量（万台）\", \"data\": [120, 85, 60, 150, 40], \"backgroundColor\": \"rgba(54, 162, 235, 0.6)\", \"borderColor\": \"rgba(54, 162, 235, 1)\", \"borderWidth\": 1}]}, \"options\": {\"responsive\": true, \"scales\": {\"y\": {\"beginAtZero\": true, \"title\": {\"display\": true, \"text\": \"销量（万台）\"}}, \"x\": {\"title\": {\"display\": true, \"text\": \"产品类型\"}}}, \"plugins\": {\"legend\": {\"display\": false}}}}"
};

      // 创建图表
      const chart = new Chart(ctx, chartConfig);

      console.log('图表创建成功', chartConfig);
    } catch (error) {
      console.error('图表创建失败:', error);
      document.querySelector('.chart-container').innerHTML =
        '<h2 style="color: red;">图表创建失败</h2><p>' + error.message + '</p>';
    }
  </script>
</body>
</html>
