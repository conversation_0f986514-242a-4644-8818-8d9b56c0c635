
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>图表生成结果</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .chart-container {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      max-width: 800px;
      margin: 0 auto;
    }
    canvas {
      max-width: 100%;
      height: auto;
    }
  </style>
</head>
<body>
  <div class="chart-container">
    <h2>图表生成结果</h2>
    <canvas id="myChart"></canvas>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    const ctx = document.getElementById('myChart');
    new Chart(ctx, {
      type:{"type": "bar", "data": {"labels": ["《百年孤独》", "《1984》", "《红楼梦》", "《追风筝的人》", "《小王子》"], "datasets": [{"label": "读者评分（满分10分）", "data": [9.2, 9.5, 9.8, 9.0, 9.6], "backgroundColor": "rgba(54, 162, 235, 0.2)", "borderColor": "rgba(54, 162, 235, 1)", "borderWidth": 1}]}, "options": {"scales": {"y": {"min": 0, "max": 10, "ticks": {"stepSize": 1}}}, "plugins": {"legend": {"display": true}}, "responsive": true}},
      data:undefined,
      options:undefined,
      locale:undefined
    });
  </script>
</body>
</html>
