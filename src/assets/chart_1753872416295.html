<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>图表生成结果</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }

    .chart-container {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      max-width: 800px;
      margin: 0 auto;
    }

    canvas {
      max-width: 100%;
      height: auto;
    }
  </style>
</head>

<body>
  <div class="chart-container">
    <h2>图表生成结果</h2>
    <canvas id="myChart"></canvas>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    const ctx = document.getElementById('myChart');
    const chartData = {
      type: "bar",
      data: {
        labels: ["手机", "笔记本电脑", "平板", "耳机", "智能手表"],
        datasets: [{ "label": "销售量", "data": [120, 85, 60, 100, 70], "backgroundColor": "rgba(54, 162, 235, 0.6)", "borderColor": "rgba(54, 162, 235, 1)", "borderWidth": 1 }]
      },
      options: { "responsive": true, "plugins": { "legend": { "display": true } }, "scales": { "x": { "display": true }, "y": { "display": true, "min": 0, "max": 150 } } },
      data: undefined, options: undefined, locale: undefined 
     };
    new Chart(ctx, chartData);
  </script>
</body>

</html>