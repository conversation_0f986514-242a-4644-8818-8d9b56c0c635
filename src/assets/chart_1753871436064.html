
<html>
<head>
  <title>图表生成结果</title>
</head>
<body>
  <div>
    <canvas id="myChart"></canvas>
  </div>
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  const ctx = document.getElementById('myChart');

  new Chart(ctx, {"type":"{\"type\": \"bar\", \"data\": {\"labels\": [\"手机\", \"笔记本电脑\", \"平板\", \"耳机\", \"智能手表\"], \"datasets\": [{\"label\": \"销量（单位：万台）\", \"data\": [120, 85, 60, 100, 45], \"backgroundColor\": \"rgba(54, 162, 235, 0.6)\", \"borderColor\": \"rgba(54, 162, 235, 1)\", \"borderWidth\": 1}]}, \"options\": {\"responsive\": true, \"plugins\": {\"legend\": {\"display\": true}}, \"scales\": {\"x\": {\"display\": true}, \"y\": {\"display\": true, \"min\": 0, \"max\": 150}}}"});
</script>
</body>
</html>
