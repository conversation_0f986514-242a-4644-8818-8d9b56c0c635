import { readFileSync, writeFileSync, mkdirSync, existsSync } from "fs"
import { join } from "path"

// 可以优化为新建html文件，然后返回html文件的url
function generate_chart(type: string, data: any, options?: any, locale?: string){
  console.log("执行图表生成")

  // 构建Chart.js配置对象，过滤掉undefined值
  const chart_config = {
    type: type,
    data: data,
    options: options,
    locale: locale
  }

  // 生成安全的JavaScript代码
  const chart_config_js = JSON.stringify(chart_config, null, 2)

  const chart_html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>图表生成结果</title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    .chart-container {
      background: white;
      padding: 30px;
      border-radius: 12px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.1);
      max-width: 900px;
      margin: 0 auto;
      backdrop-filter: blur(10px);
    }
    .chart-title {
      text-align: center;
      color: #333;
      margin-bottom: 20px;
      font-size: 24px;
      font-weight: 600;
    }
    canvas {
      max-width: 100%;
      height: auto;
    }
    .chart-info {
      margin-top: 20px;
      padding: 15px;
      background: #f8f9fa;
      border-radius: 8px;
      font-size: 14px;
      color: #666;
    }
  </style>
</head>
<body>
  <div class="chart-container">
    <h2 class="chart-title">图表生成结果</h2>
    <canvas id="myChart"></canvas>
    <div class="chart-info">
      <strong>图表类型:</strong> ${type}<br>
      <strong>生成时间:</strong> ${new Date().toLocaleString('zh-CN')}
    </div>
  </div>

  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    // 错误处理
    try {
      const ctx = document.getElementById('myChart');
      if (!ctx) {
        throw new Error('Canvas element not found');
      }

      // Chart.js配置
      const chartConfig = ${chart_config_js};

      // 创建图表
      const chart = new Chart(ctx, chartConfig);

      console.log('图表创建成功', chartConfig);
    } catch (error) {
      console.error('图表创建失败:', error);
      document.querySelector('.chart-container').innerHTML =
        '<h2 style="color: red;">图表创建失败</h2><p>' + error.message + '</p>';
    }
  </script>
</body>
</html>
`
  
  // 将图表html写入assets文件夹
  const assetsDir = "src/assets"
  if (!existsSync(assetsDir)) {
    mkdirSync(assetsDir, { recursive: true });
  }
  const chart_url = `chart_${Date.now()}.html`
  const fullPath = join(assetsDir, chart_url);
  writeFileSync(fullPath, chart_html)
  return "以下是图表生成结果，回答时需要带上完整的json数据：" + JSON.stringify(chart_config) + "\n 图表的资源地址为：" + chart_url
}

const tool_chart = {
  type: "function" as const,
  function:{
    name: "generate_chart",
    description: "当用户需要生成图表时，可以使用该工具",
    parameters: JSON.parse(readFileSync("chart.config.json", "utf-8"))
  }
} 

export { tool_chart, generate_chart } 