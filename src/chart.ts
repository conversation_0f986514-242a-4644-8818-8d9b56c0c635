import { readFileSync, writeFileSync } from "fs"
import { join } from "path"

// 可以优化为新建html文件，然后返回html文件的url
function generate_chart(type: string, data: any, options?: any, locale?: string){
  console.log("执行图表生成")
  const chart_data = {
    type: type,
    data: data,
    options: options,
    locale: locale
  }
  const chart_html = `<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>图表生成结果</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 20px;
      background-color: #f5f5f5;
    }
    .chart-container {
      background: white;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
      max-width: 800px;
      margin: 0 auto;
    }
    canvas {
      max-width: 100%;
      height: auto;
    }
  </style>
</head>
<body>
  <div class="chart-container">
    <h2>图表生成结果</h2>
    <canvas id="myChart"></canvas>
  </div>
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <script>
    const ctx = document.getElementById('myChart');
    new Chart(ctx, ${JSON.stringify(chart_data, null, 2)});
  </script>
</body>
</html>
`
  
  // 将图表html写入assets文件夹
  const chart_url = `chart_${Date.now()}.html`
  const fullPath = join("src/assets", chart_url);
  writeFileSync(fullPath, chart_html)
  
  return "以下是图表生成结果，回答时需要带上完整的json数据：" + JSON.stringify(chart_data) + "\n 图表的资源地址为：" + chart_url
}

const tool_chart = {
  type: "function" as const,
  function:{
    name: "generate_chart",
    description: "当用户需要生成图表时，可以使用该工具",
    parameters: JSON.parse(readFileSync("chart.config.json", "utf-8"))
  }
} 

export { tool_chart, generate_chart } 